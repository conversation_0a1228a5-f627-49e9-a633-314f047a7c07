/* src/index.css */

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  background-color: #f0f2f5;
  color: #333;
  margin: 0;
  padding: 20px;
  display: flex;
  justify-content: center;
}

.App {
  width: 100%;
  max-width: 700px;
  text-align: center;
}

.chat-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.controls button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.controls button:disabled {
  background-color: #a0c7ff;
  cursor: not-allowed;
}

.controls button:hover:not(:disabled) {
  background-color: #0056b3;
}

.output-box {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  min-height: 100px;
  line-height: 1.6;
}

.error-message {
  color: #d9534f; /* 红色，表示错误 */
  font-weight: bold;
}