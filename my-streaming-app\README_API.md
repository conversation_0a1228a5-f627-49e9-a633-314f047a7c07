# 毕昇工作流文件上传 API

基于 FastAPI 的文件上传和处理 API，将原始的 `test.py` 脚本转换为 Web API 服务。

## 功能特性

- 🚀 **文件上传**: 支持多种文件格式上传
- 🔄 **工作流处理**: 自动处理毕昇工作流
- 📊 **JSON 响应**: 返回结构化的 JSON 结果
- 🌐 **CORS 支持**: 支持跨域请求
- 📖 **自动文档**: 自动生成 API 文档
- ✅ **健康检查**: 提供服务状态检查

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置参数

编辑 `api_server.py` 文件中的配置：

```python
BISHENG_BASE_URL = "http://***********:3001"  # 你的毕昇服务地址
WORKFLOW_ID = "d5a8ea70bf4b40dfb7c6f43d6070faae"  # 你的工作流 ID
```

### 3. 启动服务

```bash
python api_server.py
```

服务将在 `http://localhost:8000` 启动。

## API 接口

### 1. 根路径 - 获取 API 信息
```
GET /
```

### 2. 健康检查
```
GET /health
```

### 3. 文件上传和处理 (主要接口)
```
POST /upload-and-process
```

**请求参数:**
- `file`: 要上传的文件 (multipart/form-data)
- `additional_data`: 可选的额外数据 (JSON 字符串格式)

**响应格式:**
```json
{
  "success": true,
  "data": {
    "file_name": "example.txt",
    "file_size": 1024,
    "bisheng_file_url": "http://...",
    "workflow_result": {
      "success": true,
      "data": null,
      "error": null,
      "events": [...]
    }
  },
  "events": [
    {
      "type": "input",
      "output": {...},
      "input": {...}
    }
  ]
}
```

### 4. API 文档
```
GET /docs
```
访问 Swagger UI 自动生成的 API 文档。

## 使用示例

### Python 客户端示例

```python
import requests
import json

# 上传文件并处理
url = "http://localhost:8000/upload-and-process"

with open("your_file.txt", "rb") as f:
    files = {"file": f}
    data = {
        "additional_data": json.dumps({
            "user_id": "test_user",
            "process_type": "analysis"
        })
    }
    
    response = requests.post(url, files=files, data=data)
    result = response.json()
    
    if result["success"]:
        print("处理成功!")
        print(json.dumps(result, indent=2))
    else:
        print(f"处理失败: {result['error']}")
```

### JavaScript/前端示例

```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('additional_data', JSON.stringify({
    user_id: 'test_user',
    process_type: 'analysis'
}));

fetch('http://localhost:8000/upload-and-process', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('处理成功!', data);
    } else {
        console.error('处理失败:', data.error);
    }
});
```

### cURL 示例

```bash
curl -X POST "http://localhost:8000/upload-and-process" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_file.txt" \
     -F "additional_data={\"user_id\":\"test_user\"}"
```

## 测试

运行测试客户端：

```bash
python test_api_client.py
```

这将测试：
- API 信息获取
- 健康检查
- 文件上传和处理功能

## 错误处理

API 会返回适当的 HTTP 状态码：

- `200`: 成功
- `400`: 请求参数错误
- `500`: 服务器内部错误

错误响应格式：
```json
{
  "detail": "错误描述信息"
}
```

## 部署建议

### 生产环境部署

1. **使用 Gunicorn**:
```bash
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker api_server:app --bind 0.0.0.0:8000
```

2. **使用 Docker**:
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "api_server:app", "--host", "0.0.0.0", "--port", "8000"]
```

3. **配置反向代理** (Nginx):
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 注意事项

1. **安全性**: 在生产环境中，请配置适当的 CORS 策略
2. **文件大小**: 根据需要调整文件上传大小限制
3. **超时设置**: 根据工作流复杂度调整超时时间
4. **日志记录**: 添加适当的日志记录用于监控和调试

## 许可证

MIT License
