import requests
import json
import os

def call_api_simple():
    """
    简单调用 API，只显示最终的 JSON 结果
    """
    file_path = "D:/tozhefan/测试图片/8c8a5003b91ad7281a3fcbe06309314.jpg"
    api_url = "http://localhost:8000/upload-and-process"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 - {file_path}")
        return
    
    try:
        # 上传文件并获取结果
        with open(file_path, "rb") as f:
            files = {"file": f}
            response = requests.post(api_url, files=files, timeout=120)
        
        if response.status_code == 200:
            # 直接打印返回的 JSON 结果
            result = response.json()
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("连接失败! 请确保 API 服务正在运行: python api_server.py")
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    call_api_simple()
