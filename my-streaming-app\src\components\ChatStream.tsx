// src/components/ChatStream.tsx

import React, { useState } from 'react';
// 导入我们刚刚创建的 API 方法
import { streamRequestTxt, streamRequestTxtReal } from '../api/cozeAPI';

export function ChatStream() {
  // 1. 使用 useState 管理组件状态
  const [message, setMessage] = useState(''); // 存储从 API 收到的完整消息
  const [isLoading, setIsLoading] = useState(false); // 控制按钮状态和加载提示
  const [error, setError] = useState<string | null>(null); // 存储错误信息
  const [useRealAPI, setUseRealAPI] = useState(false); // 控制是否使用真实 API

  // 2. 处理按钮点击事件的异步函数
  const handleStream = async (input: string) => {
    console.log('按钮被点击，开始处理请求:', input);
    setIsLoading(true); // 开始请求，进入加载状态
    setMessage('');     // 清空上一条消息
    setError(null);     // 清空上一次的错误

    try {
      // 3. 调用 API 请求函数
      // 关键：传入一个回调函数，定义了如何处理每一块收到的数据
      const apiFunction = useRealAPI ? streamRequestTxtReal : streamRequestTxt;
      await apiFunction(input, (chunk) => {
        console.log('收到数据块:', chunk);
        // 4. 增量更新 message 状态
        setMessage(prevMessage => prevMessage + chunk);
      });
    } catch (err: any) {
      // 如果请求过程中抛出错误，在这里捕获
      console.error('请求失败:', err);
      setError(`请求失败: ${err.message}`);
    } finally {
      // 5. 请求结束后（无论成功还是失败），取消加载状态
      console.log('请求结束，设置 isLoading 为 false');
      setIsLoading(false);
    }
  };

  // 添加一个模拟的流式函数用于测试
  const handleMockStream = async () => {
    console.log('开始模拟流式响应');
    setIsLoading(true);
    setMessage('');
    setError(null);

    try {
      const mockResponse = "你好！我是一个AI助手，很高兴为您服务。我可以帮助您解答问题、提供信息和进行对话。";

      // 模拟逐字显示
      for (let i = 0; i < mockResponse.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 50)); // 50ms 延迟
        setMessage(prev => prev + mockResponse[i]);
      }
    } catch (err: any) {
      setError(`模拟请求失败: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 添加 API 连接测试函数
  const testAPIConnection = async () => {
    console.log('测试 API 连接...');
    setError(null);

    try {
      const response = await fetch('https://api.coze.cn/v3/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer pat_WeHWcj3v6Hy3Wy3HaOJmlftZhI5ZwnBtezt1ATaXoYULmzivx2yeLnkXIuUmPCUF'
        },
        body: JSON.stringify({
          bot_id: "7532327725139509300",
          user_id: "7350058012570452998",
          stream: false, // 非流式测试
          auto_save_history: false,
          additional_messages: [{
            role: "user",
            content: "测试连接",
            content_type: "text"
          }]
        })
      });

      console.log('API 测试响应状态:', response.status);
      const result = await response.text();
      console.log('API 测试响应内容:', result);

      if (response.ok) {
        setMessage('API 连接测试成功！\n响应: ' + result);
      } else {
        setError(`API 连接测试失败: ${response.status} - ${result}`);
      }
    } catch (err: any) {
      console.error('API 连接测试错误:', err);
      setError(`API 连接测试失败: ${err.message}`);
    }
  };

  return (
    <div className="chat-container">
      <h3>流式数据处理示例</h3>

      {/* API 模式切换 */}
      <div style={{ marginBottom: '15px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
          <input
            type="checkbox"
            checked={useRealAPI}
            onChange={(e) => setUseRealAPI(e.target.checked)}
            style={{ marginRight: '8px' }}
          />
          使用真实 Coze API（需要有效的 API Key 和 Bot ID）
        </label>
        <small style={{ color: '#666', marginTop: '5px', display: 'block' }}>
          {useRealAPI
            ? '⚠️ 当前使用真实 API - 需要配置有效的凭据'
            : '✅ 当前使用模拟 API - 演示流式数据处理效果'
          }
        </small>
      </div>

      <div className="controls">
        <button
          onClick={() => {
            console.log('流式请求按钮被点击');
            handleStream('你好，请用 50 字介绍一下你自己');
          }}
          disabled={isLoading}
          style={{
            padding: '12px 24px',
            fontSize: '16px',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            backgroundColor: isLoading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            marginRight: '10px',
            fontWeight: 'bold'
          }}
        >
          {isLoading ? '正在处理...' : '开始流式对话'}
        </button>

        {!useRealAPI && (
          <button
            onClick={() => {
              console.log('独立模拟按钮被点击');
              handleMockStream();
            }}
            disabled={isLoading}
            style={{
              padding: '12px 24px',
              fontSize: '16px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              backgroundColor: isLoading ? '#ccc' : '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              marginRight: '10px'
            }}
          >
            {isLoading ? '正在模拟...' : '独立模拟演示'}
          </button>
        )}

        {useRealAPI && (
          <button
            onClick={() => {
              console.log('API 连接测试按钮被点击');
              testAPIConnection();
            }}
            disabled={isLoading}
            style={{
              padding: '12px 24px',
              fontSize: '16px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              backgroundColor: isLoading ? '#ccc' : '#ffc107',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              marginRight: '10px'
            }}
          >
            测试 API 连接
          </button>
        )}

        <button
          onClick={() => {
            setMessage('');
            setError(null);
            console.log('消息已清除');
          }}
          style={{
            padding: '12px 24px',
            fontSize: '16px',
            cursor: 'pointer',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '6px'
          }}
        >
          清除消息
        </button>
      </div>
      
      {/* 6. 内容展示区域 */}
      <div className="output-box">
        {/* 如果有错误，优先显示错误信息 */}
        {error ? (
          <p className="error-message">{error}</p>
        ) : (
          /* white-space: 'pre-wrap' 可以保留文本中的换行和空格 */
          <p style={{ whiteSpace: 'pre-wrap' }}>{message}</p>
        )}
      </div>
    </div>
  );
}
