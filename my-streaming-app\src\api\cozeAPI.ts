// src/api/cozeAPI.ts

/**
 * 模拟流式 API 响应 - 用于演示流式数据处理
 * @param content 发送的消息内容
 * @param onDataReceived 接收到数据块时的回调函数，用于实时更新UI
 */
export const streamRequestTxt = async (content: string, onDataReceived: (chunk: string) => void) => {
  console.log('streamRequestTxt 函数被调用，参数:', content);

  // 模拟 AI 响应内容
  const mockResponses = [
    "你好！我是一个AI助手。",
    "我可以帮助你解答各种问题，",
    "包括编程、学习、生活等方面的内容。",
    "如果你有任何疑问，",
    "随时可以向我提问。",
    "我会尽力为你提供有用的信息和建议。"
  ];

  try {
    console.log('开始模拟流式响应...');

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    // 逐句发送响应
    for (let i = 0; i < mockResponses.length; i++) {
      const sentence = mockResponses[i];
      console.log(`发送第 ${i + 1} 句:`, sentence);

      // 逐字符发送，模拟真实的流式效果
      for (let j = 0; j < sentence.length; j++) {
        await new Promise(resolve => setTimeout(resolve, 50)); // 50ms 延迟
        onDataReceived(sentence[j]);
      }

      // 句子之间的停顿
      await new Promise(resolve => setTimeout(resolve, 200));
      if (i < mockResponses.length - 1) {
        onDataReceived('\n'); // 换行
      }
    }

    console.log('模拟流式响应完成');
  } catch (error) {
    console.error('模拟流式请求失败:', error);
    throw error;
  }
};

/**
 * 原始的 Coze API 流式请求方法（当你有有效的 API 配置时使用）
 * @param content 发送给机器人的消息内容
 * @param onDataReceived 接收到数据块时的回调函数，用于实时更新UI
 */
export const streamRequestTxtReal = async (content: string, onDataReceived: (chunk: string) => void) => {
  console.log('streamRequestTxtReal 函数被调用，参数:', content);
  try {
    // 1. 发起 fetch 请求，关键在于设置 stream: true
    console.log('开始发起 fetch 请求...');
    const response = await fetch('https://api.coze.cn/v3/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 重要提示：切勿将 API Key 直接硬编码在前端代码中！
        // 在生产环境中，这应该通过后端代理来调用，以保护你的 Key。
        'Authorization': 'Bearer pat_WeHWcj3v6Hy3Wy3HaOJmlftZhI5ZwnBtezt1ATaXoYULmzivx2yeLnkXIuUmPCUF'
      },
      body: JSON.stringify({
        bot_id: "7532327725139509300", // 使用你的有效 bot_id
        user_id: "7350058012570452998", // 使用你的有效 user_id
        stream: true, // 开启流式响应
        auto_save_history: true,
        additional_messages: [{
          role: "user",
          content: content,
          content_type: "text"
        }]
      })
    });

    // 2. 检查响应是否成功
    console.log('收到响应，状态码:', response.status);
    if (!response.ok) {
      console.error('HTTP 错误! 状态码:', response.status);
      throw new Error(`HTTP 错误! 状态码: ${response.status}`);
    }

    // 3. 获取流式读取器 (ReadableStream Reader)
    console.log('检查响应体:', response.body);
    const reader = response.body?.getReader();
    if (!reader) {
      console.error('无法获取响应的读取器');
      throw new Error('无法获取响应的读取器。');
    }
    console.log('成功获取读取器');

    // 4. 创建解码器，将二进制数据流转换为文本
    const decoder = new TextDecoder('utf-8');
    let partialData = ''; // 用于存储未处理完的数据片段
    let chunkCount = 0; // 计数器

    console.log('开始读取数据流...');
    // 5. 循环读取数据流
    while (true) {
      console.log('等待读取数据块...');
      const { done, value } = await reader.read();
      chunkCount++;

      console.log(`读取第 ${chunkCount} 个数据块, done: ${done}, value:`, value);

      // 如果 done 为 true，说明数据流已结束
      if (done) {
        console.log('数据流读取完成');
        break;
      }

      // 将接收到的二进制块 (Uint8Array) 解码为字符串
      const chunk = decoder.decode(value, { stream: true });
      console.log('解码后的数据块:', chunk);
      partialData += chunk;

      // 6. 按行处理数据 (因为流式数据通常以换行符分隔)
      const lines = partialData.split('\n');
      console.log('分割后的行数:', lines.length, '行内容:', lines);
      partialData = lines.pop() || ''; // 保留最后一行不完整的，等待下一个数据块

      for (const line of lines) {
        console.log('处理数据行:', line); // 添加调试日志

        // Coze API 返回的事件流通常以 'data:' 开头
        if (line.startsWith('data:')) {
          const jsonData = line.replace('data:', '').trim(); // 移除 'data:' 前缀并去除空格
          console.log('提取的 JSON 数据:', jsonData); // 添加调试日志

          // 跳过空的 data 行
          if (!jsonData || jsonData === '') {
            console.log('跳过空的 data 行');
            continue;
          }

          try {
            const parsed = JSON.parse(jsonData);
            console.log('解析后的数据:', parsed); // 添加调试日志

            // 检查是否是错误响应
            if (parsed.code && parsed.code !== 0) {
              console.error('API 返回错误:', parsed);
              throw new Error(`API 错误 ${parsed.code}: ${parsed.msg}`);
            }

            // 确保解析出的数据包含我们需要的 content 字段
            if (parsed.content !== undefined && parsed.role === 'assistant' && parsed.type === 'answer') {
              // Coze API 的标准格式：直接包含 content 字段
              console.log('找到 Coze 内容:', parsed.content);
              onDataReceived(parsed.content);
            } else if (parsed.message && parsed.message.type === 'answer' && parsed.message.content) {
              // 备用格式：嵌套在 message 中
              console.log('找到嵌套内容:', parsed.message.content);
              onDataReceived(parsed.message.content);
            } else if (parsed.event === 'conversation.message.delta' && parsed.data && parsed.data.content) {
              // 备用格式：事件格式
              console.log('找到事件内容:', parsed.data.content);
              onDataReceived(parsed.data.content);
            } else {
              console.log('数据结构不匹配，完整数据:', JSON.stringify(parsed, null, 2));
            }
          } catch (e) {
            console.error('JSON 解析失败:', jsonData, e);
            // 如果是 API 错误，向上抛出
            if (e instanceof Error && e.message.includes('API 错误')) {
              throw e;
            }
          }
        } else if (line.trim() !== '') {
          console.log('非 data 行:', line);
        }
      }
    }
  } catch (error) {
    console.error('流式请求失败:', error);

    // 检查是否是网络错误
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('网络连接错误，可能是 CORS 或网络问题');
      throw new Error('网络连接失败，请检查网络连接或 CORS 设置');
    }

    // 将错误向上抛出，以便 UI 层可以捕获它
    throw error;
  }
};