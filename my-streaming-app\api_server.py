from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import requests
import json
import os
import tempfile
import shutil
from typing import Optional, Dict, Any
import uvicorn
from pydantic import BaseModel

# ==============================================================================
# 配置信息
# ==============================================================================
BISHENG_BASE_URL = "http://***********:3001"
WORKFLOW_ID = "d5a8ea70bf4b40dfb7c6f43d6070faae"

app = FastAPI(
    title="毕昇工作流文件上传 API",
    description="基于毕昇工作流的文件上传和处理 API",
    version="1.0.0"
)

# 添加 CORS 中间件，允许前端调用
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class BiShengWorkflowAPI:
    """
    毕昇工作流 API 客户端，专门用于 API 服务
    """
    def __init__(self, base_url: str, workflow_id: str):
        self.base_url = base_url
        self.invoke_url = f"{self.base_url}/api/v2/workflow/invoke"
        self.upload_url = f"{self.base_url}/api/v1/knowledge/upload"
        self.workflow_id = workflow_id
        
        self.session_id = None
        self.waiting_input_node_id = None
        self.message_id = None
        self.required_inputs = []
        
        self.headers = {'Content-Type': 'application/json'}

    def upload_file_to_bisheng(self, file_path: str) -> Optional[str]:
        """
        上传文件到毕昇服务
        """
        try:
            with open(file_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(self.upload_url, files=files, timeout=60)
                response.raise_for_status()
                
            result = response.json()
            file_path = result['data'].get('file_path', '')
            return file_path if file_path else None
            
        except Exception as e:
            print(f"文件上传失败: {e}")
            return None

    def _invoke(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送请求到 invoke 接口"""
        try:
            response = requests.post(
                self.invoke_url, 
                headers=self.headers, 
                data=json.dumps(payload), 
                timeout=120
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"请求失败: {e}")
            return None

    def process_workflow(self, file_url: str, additional_inputs: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理完整的工作流程
        """
        result = {
            "success": False,
            "data": None,
            "error": None,
            "events": []
        }
        
        try:
            # 1. 启动工作流
            payload = {"workflow_id": self.workflow_id, "stream": False}
            response = self._invoke(payload)
            
            if not response:
                result["error"] = "启动工作流失败"
                return result
            
            # 2. 处理工作流事件
            while True:
                events = self._handle_events(response)
                result["events"].extend(events)
                
                if self.waiting_input_node_id:
                    # 需要输入，构建输入数据
                    node_inputs = {}
                    
                    # 处理文件输入
                    for field in self.required_inputs:
                        key = field.get("key")
                        field_type = field.get("type")
                        
                        if field_type == 'file':
                            node_inputs[key] = [file_url]
                        elif additional_inputs and key in additional_inputs:
                            node_inputs[key] = additional_inputs[key]
                    
                    # 发送输入
                    send_payload = {
                        "workflow_id": self.workflow_id,
                        "stream": False,
                        "session_id": self.session_id,
                        "message_id": self.message_id,
                        "input": {
                            self.waiting_input_node_id: node_inputs
                        }
                    }
                    
                    self.waiting_input_node_id = None
                    self.required_inputs = []
                    
                    response = self._invoke(send_payload)
                    if not response:
                        result["error"] = "发送输入失败"
                        return result
                        
                else:
                    # 工作流完成
                    break
            
            result["success"] = True
            return result
            
        except Exception as e:
            result["error"] = str(e)
            return result

    def _handle_events(self, response_data: Dict[str, Any]) -> list:
        """处理事件并返回事件列表"""
        events = []
        
        if not response_data or "data" not in response_data:
            return events

        if "session_id" in response_data["data"]:
            self.session_id = response_data["data"]["session_id"]

        api_events = response_data["data"].get("events", [])
        
        for event in api_events:
            event_type = event.get("event")
            output_schema = event.get("output_schema")
            input_schema = event.get("input_schema")
            
            event_info = {
                "type": event_type,
                "output": output_schema,
                "input": input_schema
            }
            events.append(event_info)

            if event_type in ("input", "output_with_input_msg"):
                self.waiting_input_node_id = event.get("node_id")
                self.message_id = event.get("message_id")
                self.required_inputs = input_schema.get("value", [])

        return events

def extract_final_json_from_workflow(workflow_result: Dict[str, Any]) -> Optional[Any]:
    """
    从工作流结果中提取最终的 JSON 数据
    """
    try:
        events = workflow_result.get('events', [])

        for event in events:
            if event.get('type') == 'stream_msg':
                output = event.get('output', {})
                message = output.get('message', '')

                # 提取 JSON 内容（去掉 ```json 标记）
                if message and '```json' in message:
                    # 提取 JSON 部分
                    start = message.find('```json') + 7
                    end = message.find('```', start)
                    if end != -1:
                        json_str = message[start:end].strip()
                        try:
                            return json.loads(json_str)
                        except json.JSONDecodeError:
                            continue

        return None
    except Exception as e:
        print(f"提取 JSON 时发生错误: {e}")
        return None

# 创建全局客户端实例
bisheng_client = BiShengWorkflowAPI(BISHENG_BASE_URL, WORKFLOW_ID)

class ProcessResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    events: list = []

@app.get("/")
async def root():
    """API 根路径"""
    return {
        "message": "毕昇工作流文件上传 API",
        "version": "1.0.0",
        "endpoints": {
            "/upload-and-process": "POST - 上传文件并处理工作流",
            "/docs": "GET - API 文档",
            "/health": "GET - 健康检查"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "bisheng-workflow-api"}

@app.post("/upload-and-process")
async def upload_and_process_file(
    file: UploadFile = File(...),
    additional_data: Optional[str] = Form(None)
):
    """
    上传文件并处理工作流

    - **file**: 要上传的文件
    - **additional_data**: 可选的额外数据 (JSON 字符串格式)

    直接返回提取的最终 JSON 数据
    """
    temp_file_path = None
    
    try:
        # 1. 保存上传的文件到临时目录
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
            temp_file_path = temp_file.name
            shutil.copyfileobj(file.file, temp_file)
        
        # 2. 上传文件到毕昇服务
        bisheng_file_url = bisheng_client.upload_file_to_bisheng(temp_file_path)
        
        if not bisheng_file_url:
            raise HTTPException(status_code=500, detail="文件上传到毕昇服务失败")
        
        # 3. 解析额外数据
        additional_inputs = {}
        if additional_data:
            try:
                additional_inputs = json.loads(additional_data)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="additional_data 必须是有效的 JSON 字符串")
        
        # 4. 处理工作流
        result = bisheng_client.process_workflow(bisheng_file_url, additional_inputs)

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"工作流处理失败: {result['error']}")

        # 5. 提取最终的 JSON 结果
        final_json_result = extract_final_json_from_workflow(result)

        if final_json_result is None:
            raise HTTPException(status_code=500, detail="未能从工作流结果中提取有效的 JSON 数据")

        # 6. 直接返回提取的 JSON 结果
        return final_json_result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理过程中发生错误: {str(e)}")
    
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

if __name__ == "__main__":
    print("🚀 启动毕昇工作流文件上传 API 服务...")
    print(f"📡 毕昇服务地址: {BISHENG_BASE_URL}")
    print(f"🔧 工作流 ID: {WORKFLOW_ID}")
    print("📖 API 文档: http://localhost:8000/docs")
    print("🌐 服务地址: http://localhost:8000")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
