import requests
import json
import os


"""
json返回：

Generated json

[

  {

    "date": "string",

    "type": "string",

    "total_amount": "number",

    "currency": "string"

  }

]

"""



# ==============================================================================
# 1. 【重要】请在这里配置您的信息
# ==============================================================================
# 毕昇服务的完整根地址，必须是此脚本可以访问到的地址
# 例如: "http://*************:5000" 或 "https://bisheng.dataelem.com"
BISHENG_BASE_URL = "http://***********:3001" 

# 你的工作流 ID
WORKFLOW_ID = "d5a8ea70bf4b40dfb7c6f43d6070faae"  # 请替换成您自己的 workflow_id

class BiShengWorkflowClient:
    """
    一个用于与毕昇工作流 API 交互的、支持文件上传的客户端。
    """
    def __init__(self, base_url, workflow_id):
        if base_url == "http://YOUR_BISHENG_IP:PORT":
            raise ValueError("请在代码中配置您的 BISHENG_BASE_URL")
        if workflow_id == "YOUR_WORKFLOW_ID":
            raise ValueError("请在代码中配置您的 WORKFLOW_ID")
            
        self.base_url = base_url
        self.invoke_url = f"{self.base_url}/api/v2/workflow/invoke"
        self.upload_url = f"{self.base_url}/api/v1/knowledge/upload"
        self.workflow_id = workflow_id
        
        self.session_id = None
        self.waiting_input_node_id = None
        self.message_id = None
        self.required_inputs = [] # 存储当前需要输入的字段信息
        
        self.headers = {'Content-Type': 'application/json'}

    def upload_file(self, local_path: str):
        """
        上传本地文件到毕昇服务。
        """
        if not os.path.exists(local_path):
            print(f"\n[错误] 本地文件不存在: {local_path}")
            return None

        print(f"  正在上传文件 '{os.path.basename(local_path)}' 到 {self.upload_url}...")
        try:
            with open(local_path, 'rb') as f:
                files = {'file': f}
                res = requests.post(self.upload_url, files=files, timeout=60)
                res.raise_for_status()
                
            file_path = res.json()['data'].get('file_path', '')
            if file_path:
                print(f"  文件上传成功! 服务器路径: {file_path}")
                return file_path
            else:
                print(f"\n[错误] 文件上传后未能获取服务器路径。响应: {res.text}")
                return None
        except Exception as e:
            print(f"\n[错误] 文件上传失败: {e}")
            return None
            
    def _invoke(self, payload):
        """私有方法，用于发送请求到 invoke 接口。"""
        try:
            print("\n[客户端 -> 服务器] 正在发送请求...")
            response = requests.post(self.invoke_url, headers=self.headers, data=json.dumps(payload), timeout=120)
            response.raise_for_status()
            response_json = response.json()
            return response_json
        except requests.exceptions.RequestException as e:
            print(f"\n[错误] 请求失败: {e}")
            return None
        except json.JSONDecodeError:
            print(f"\n[错误] 无法解析服务器响应: {response.text}")
            return None

    def handle_events(self, response_data):
        """处理从 API 返回的事件列表。"""
        if not response_data or "data" not in response_data:
            return False

        if "session_id" in response_data["data"]:
            self.session_id = response_data["data"]["session_id"]

        events = response_data["data"].get("events", [])
        if not events:
            return True

        for event in events:
            event_type = event.get("event")
            output_schema = event.get("output_schema")
            input_schema = event.get("input_schema")
            
            print("-" * 50)
            print(f"  收到事件: {event_type}")

            if output_schema and "message" in output_schema and output_schema["message"]:
                print(f"\n🤖 [AI]: {output_schema['message']}")

            if event_type in ("input", "output_with_input_msg"):
                self.waiting_input_node_id = event.get("node_id")
                self.message_id = event.get("message_id")
                self.required_inputs = input_schema.get("value", [])
                
                print("\n📝 工作流需要您提供以下信息:")
                for field in self.required_inputs:
                    label = field.get('label')
                    key = field.get('key')
                    field_type = field.get('type')
                    print(f"   - 提示: '{label}' (字段名: {key}, 类型: {field_type})")
                
                return False # 表示流程暂停，等待用户输入

            elif event_type == "close":
                print("\n[流程结束]")
                return "close"
        
        return True

    def start(self):
        """启动工作流。"""
        print("🚀 正在启动工作流...")
        payload = {"workflow_id": self.workflow_id, "stream": False}
        response = self._invoke(payload)
        if response:
            return self.handle_events(response)
        return False

    def send(self, input_data):
        """向等待输入的节点发送用户构建好的表单数据。"""
        payload = {
            "workflow_id": self.workflow_id,
            "stream": False,
            "session_id": self.session_id,
            "message_id": self.message_id,
            "input": {
                self.waiting_input_node_id: input_data
            }
        }
        
        self.waiting_input_node_id = None
        self.required_inputs = []

        response = self._invoke(payload)
        if response:
            return self.handle_events(response)
        return False

def main():
    """主函数，运行整个交互流程。"""
    os.system('cls' if os.name == 'nt' else 'clear')
    print("=" * 60)
    print("       欢迎使用毕昇工作流 Python 客户端 (文件上传版)")
    print("=" * 60)
    
    try:
        client = BiShengWorkflowClient(BISHENG_BASE_URL, WORKFLOW_ID)
    except ValueError as e:
        print(f"[配置错误] {e}")
        return

    status = client.start()
    
    while status != "close":
        if status is False: # 等待用户输入
            try:
                # 动态构建用户输入
                node_inputs = {}
                for field in client.required_inputs:
                    key = field.get("key")
                    label = field.get("label")
                    field_type = field.get("type")

                    if field_type == 'file':
                        # 这是文件输入类型
                        while True:
                            local_file_path = input(f"📄 请输入 '{label}' 的本地文件路径: ")
                            bisheng_url = client.upload_file(local_file_path)
                            if bisheng_url:
                                node_inputs[key] = [bisheng_url] # 文件URL需要是列表形式
                                break
                    else:
                        # 这是普通文本/选择输入类型
                        user_value = input(f"⌨️  请输入 '{label}': ")
                        node_inputs[key] = user_value
                
                # 所有需要的输入都收集完毕后，发送
                status = client.send(node_inputs)

            except (KeyboardInterrupt, EOFError):
                print("\n再见！")
                break
        else: 
            break

if __name__ == "__main__":
    main()

