import requests
import json
import os

def test_upload_api():
    """
    测试文件上传 API
    """
    api_url = "http://localhost:8000/upload-and-process"
    
    # 测试文件路径 - 请替换为你要测试的文件路径
    test_file_path = "test_document.txt"
    
    # 如果测试文件不存在，创建一个
    if not os.path.exists(test_file_path):
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文档。\n")
            f.write("用于测试毕昇工作流 API 的文件上传功能。\n")
            f.write("包含一些示例内容。")
        print(f"✅ 创建了测试文件: {test_file_path}")
    
    try:
        print("🚀 开始测试文件上传 API...")
        
        # 准备文件和数据
        with open(test_file_path, 'rb') as f:
            files = {'file': (test_file_path, f, 'text/plain')}
            
            # 可选的额外数据
            additional_data = {
                "user_id": "test_user",
                "process_type": "document_analysis"
            }
            
            data = {
                'additional_data': json.dumps(additional_data)
            }
            
            # 发送请求
            response = requests.post(api_url, files=files, data=data, timeout=120)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功!")
            print("📄 返回结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 提取关键信息
            if result.get("success"):
                data = result.get("data", {})
                print(f"\n📁 文件名: {data.get('file_name')}")
                print(f"📏 文件大小: {data.get('file_size')} 字节")
                print(f"🔗 毕昇文件URL: {data.get('bisheng_file_url')}")
                
                events = result.get("events", [])
                print(f"📋 工作流事件数量: {len(events)}")
                
        else:
            print("❌ 请求失败!")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保 API 服务正在运行 (python api_server.py)")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_health_check():
    """
    测试健康检查接口
    """
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查通过!")
            print(f"响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查错误: {e}")

def test_api_info():
    """
    测试 API 信息接口
    """
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            print("📖 API 信息:")
            print(json.dumps(response.json(), indent=2, ensure_ascii=False))
        else:
            print(f"❌ 获取 API 信息失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取 API 信息错误: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("       毕昇工作流 API 测试客户端")
    print("=" * 60)
    
    print("\n1. 测试 API 信息...")
    test_api_info()
    
    print("\n2. 测试健康检查...")
    test_health_check()
    
    print("\n3. 测试文件上传和处理...")
    test_upload_api()
    
    print("\n🎉 测试完成!")
